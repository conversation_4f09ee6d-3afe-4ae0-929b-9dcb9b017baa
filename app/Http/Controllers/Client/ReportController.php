<?php

namespace App\Http\Controllers\Client;

use App\Enums\TransactionTypeEnum;
use App\Models\Transaction;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class ReportController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/common.min.js', 'assets/js/clients/datatable-report.min.js']);
        return view('clients.reports.index');
    }

    public function filter(Request $request)
    {
        $type = $request->query('type');
        $keyword = $request->query('keyword');
        $tenantID = is_object($request->tenant) ? $request->tenant->id : $request->tenant;
        $userID = auth()->user()->id;

        if ($type === 'plus') {
            $query = Transaction::query()
                ->where('tenant_id', $tenantID)
                ->where('user_id', $userID)
                ->where('type', TransactionTypeEnum::PLUS_MONEY->value)
                ->select('id', 'code', 'type', 'amount', 'balance_before', 'balance_after', 'math', 'status', 'description', 'created_at')
                ->orderBy('created_at', 'desc');

            return DataTables::of($query)
                ->filter(function ($query) use ($keyword) {
                    if ($keyword) {
                        $query->where('code', 'like', '%' . $keyword . '%');
                    }
                })
                ->rawColumns(['type', 'status'])
                ->make(true);
        } else if ($type === 'minus') {
            $query = Transaction::query()
                ->where('tenant_id', $tenantID)
                ->where('user_id', $userID)
                ->where('type', TransactionTypeEnum::MINUS_MONEY->value)
                ->select('id', 'code', 'type', 'amount', 'balance_before', 'balance_after', 'math', 'status', 'description', 'created_at')
                ->orderBy('created_at', 'desc');

            return DataTables::of($query)
                ->filter(function ($query) use ($keyword) {
                    if ($keyword) {
                        $query->where('code', 'like', '%' . $keyword . '%');
                    }
                })
                ->rawColumns(['type', 'status'])
                ->make(true);

        }  else {
            $query = Transaction::query()
                ->where('tenant_id', $tenantID)
                ->where('user_id', $userID)
                ->select('id', 'code', 'type', 'amount', 'balance_before', 'balance_after', 'math', 'status', 'description', 'created_at')
                ->orderBy('created_at', 'desc');

            return DataTables::of($query)
                ->filter(function ($query) use ($keyword) {
                    if ($keyword) {
                        $query->where('code', 'like', '%' . $keyword . '%');
                    }
                })
                ->rawColumns(['type', 'status'])
                ->make(true);
        }
    }

}
